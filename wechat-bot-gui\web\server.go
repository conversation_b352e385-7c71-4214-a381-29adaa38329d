package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"

	"wechat-bot-gui/bot"
	"wechat-bot-gui/config"
)

// WebServer Web服务器
type WebServer struct {
	bot    *bot.WeChatBot
	config *config.Config
	logs   []string
	mutex  sync.RWMutex
}

// LogEntry 日志条目
type LogEntry struct {
	Message string `json:"message"`
	Time    string `json:"time"`
}

// StatusResponse 状态响应
type StatusResponse struct {
	IsLoggedIn bool   `json:"isLoggedIn"`
	Status     string `json:"status"`
}

// SendRequest 发送消息请求
type SendRequest struct {
	Target  string `json:"target"`
	Message string `json:"message"`
}

// NewWebServer 创建Web服务器
func NewWebServer() *WebServer {
	cfg, err := config.LoadConfig()
	if err != nil {
		cfg = config.DefaultConfig()
	}

	wechatBot := bot.NewWeChatBot(cfg)

	server := &WebServer{
		bot:    wechatBot,
		config: cfg,
		logs:   []string{},
	}

	// 设置回调
	server.setupCallbacks()

	return server
}

// setupCallbacks 设置回调函数
func (ws *WebServer) setupCallbacks() {
	ws.bot.OnLoginSuccess = func() {
		ws.addLog("✅ 登录成功！")
	}

	ws.bot.OnLoginFailed = func(err error) {
		ws.addLog(fmt.Sprintf("❌ 登录失败: %v", err))
	}

	ws.bot.OnLogout = func() {
		ws.addLog("👋 已退出登录")
	}

	ws.bot.OnMessage = func(message string) {
		ws.addLog(message)
	}

	ws.bot.OnQRCode = func(qrURL string) {
		ws.addLog("📱 请扫描二维码登录: " + qrURL)
	}
}

// addLog 添加日志
func (ws *WebServer) addLog(message string) {
	ws.mutex.Lock()
	defer ws.mutex.Unlock()

	ws.logs = append(ws.logs, message)
	if len(ws.logs) > 100 {
		ws.logs = ws.logs[10:]
	}
}

// getLogs 获取日志
func (ws *WebServer) getLogs() []string {
	ws.mutex.RLock()
	defer ws.mutex.RUnlock()

	result := make([]string, len(ws.logs))
	copy(result, ws.logs)
	return result
}

// 处理首页
func (ws *WebServer) handleHome(w http.ResponseWriter, r *http.Request) {
	tmpl := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信推送机器人控制台</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 10px; border-radius: 5px; margin-bottom: 20px; text-align: center; font-weight: bold; }
        .status.logged-in { background-color: #d4edda; color: #155724; }
        .status.logged-out { background-color: #f8d7da; color: #721c24; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn:hover { opacity: 0.8; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .form-group textarea { height: 80px; resize: vertical; }
        .logs { background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 微信推送机器人控制台</h1>
            <p>简单易用的微信自动化工具</p>
        </div>

        <div id="status" class="status logged-out">
            ⭕ 状态: 未登录
        </div>

        <div class="grid">
            <div class="section">
                <h3>🔑 登录控制</h3>
                <button id="loginBtn" class="btn btn-primary" onclick="login()">登录微信</button>
                <button id="logoutBtn" class="btn btn-danger" onclick="logout()" disabled>退出登录</button>
                <p><small>点击登录后，请在日志中查看二维码链接</small></p>
            </div>

            <div class="section">
                <h3>⚙️ 设置</h3>
                <label>
                    <input type="checkbox" id="autoReply" onchange="toggleAutoReply()"> 
                    启用自动回复
                </label>
                <p><small>开启后会根据预设关键词自动回复消息</small></p>
            </div>
        </div>

        <div class="section">
            <h3>📤 发送消息</h3>
            <div class="form-group">
                <label for="target">发送目标 (好友昵称或群名称):</label>
                <input type="text" id="target" placeholder="请输入好友昵称或群名称">
            </div>
            <div class="form-group">
                <label for="message">消息内容:</label>
                <textarea id="message" placeholder="请输入要发送的消息内容"></textarea>
            </div>
            <button class="btn btn-success" onclick="sendMessage()">发送消息</button>
        </div>

        <div class="section">
            <h3>📋 运行日志</h3>
            <div id="logs" class="logs">
                欢迎使用微信推送机器人控制台！<br>
                请点击"登录微信"开始使用。
            </div>
            <button class="btn" onclick="clearLogs()">清空日志</button>
            <button class="btn" onclick="refreshLogs()">刷新日志</button>
        </div>
    </div>

    <script>
        let isLoggedIn = false;

        // 登录
        function login() {
            fetch('/api/login', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('开始登录流程...');
                        document.getElementById('loginBtn').disabled = true;
                    } else {
                        alert('登录失败: ' + data.error);
                    }
                });
        }

        // 退出登录
        function logout() {
            fetch('/api/logout', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addLog('已退出登录');
                    }
                });
        }

        // 发送消息
        function sendMessage() {
            const target = document.getElementById('target').value.trim();
            const message = document.getElementById('message').value.trim();

            if (!target) {
                alert('请输入发送目标');
                return;
            }

            if (!message) {
                alert('请输入消息内容');
                return;
            }

            fetch('/api/send', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ target: target, message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('message').value = '';
                    addLog('消息发送成功: ' + target);
                } else {
                    alert('发送失败: ' + data.error);
                }
            });
        }

        // 切换自动回复
        function toggleAutoReply() {
            const enabled = document.getElementById('autoReply').checked;
            fetch('/api/autoreply', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ enabled: enabled })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('自动回复已' + (enabled ? '启用' : '禁用'));
                }
            });
        }

        // 添加日志
        function addLog(message) {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            logs.innerHTML += '[' + time + '] ' + message + '<br>';
            logs.scrollTop = logs.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 刷新日志
        function refreshLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    const logs = document.getElementById('logs');
                    logs.innerHTML = data.logs.join('<br>');
                    logs.scrollTop = logs.scrollHeight;
                });
        }

        // 更新状态
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('status');
                    const loginBtn = document.getElementById('loginBtn');
                    const logoutBtn = document.getElementById('logoutBtn');

                    if (data.isLoggedIn) {
                        statusDiv.textContent = '✅ 状态: 已登录';
                        statusDiv.className = 'status logged-in';
                        loginBtn.disabled = true;
                        logoutBtn.disabled = false;
                        isLoggedIn = true;
                    } else {
                        statusDiv.textContent = '⭕ 状态: 未登录';
                        statusDiv.className = 'status logged-out';
                        loginBtn.disabled = false;
                        logoutBtn.disabled = true;
                        isLoggedIn = false;
                    }
                });
        }

        // 定期更新状态和日志
        setInterval(updateStatus, 2000);
        setInterval(refreshLogs, 3000);

        // 页面加载时初始化
        window.onload = function() {
            updateStatus();
            refreshLogs();
        };
    </script>
</body>
</html>
`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprint(w, tmpl)
}

// 处理登录API
func (ws *WebServer) handleLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if ws.bot.IsLoggedIn() {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   "已经登录了",
		})
		return
	}

	go func() {
		if err := ws.bot.Login(); err != nil {
			ws.addLog(fmt.Sprintf("登录失败: %v", err))
		}
	}()

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
	})
}

// 处理退出登录API
func (ws *WebServer) handleLogout(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	ws.bot.Logout()

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
	})
}

// 处理状态API
func (ws *WebServer) handleStatus(w http.ResponseWriter, r *http.Request) {
	status := "未登录"
	if ws.bot.IsLoggedIn() {
		status = "已登录"
	}

	response := StatusResponse{
		IsLoggedIn: ws.bot.IsLoggedIn(),
		Status:     status,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 处理发送消息API
func (ws *WebServer) handleSend(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if !ws.bot.IsLoggedIn() {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   "请先登录微信",
		})
		return
	}

	var req SendRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   "请求格式错误",
		})
		return
	}

	target := strings.TrimSpace(req.Target)
	message := strings.TrimSpace(req.Message)

	if target == "" || message == "" {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   "目标和消息内容不能为空",
		})
		return
	}

	// 先尝试发送给好友
	err := ws.bot.SendTextToFriend(target, message)
	if err != nil {
		// 如果发送给好友失败，尝试发送给群组
		err = ws.bot.SendTextToGroup(target, message)
	}

	if err != nil {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		})
	} else {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
		})
	}
}

// 处理自动回复API
func (ws *WebServer) handleAutoReply(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Enabled bool `json:"enabled"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": false,
			"error":   "请求格式错误",
		})
		return
	}

	ws.config.AutoReply.Enabled = req.Enabled
	config.SaveConfig(ws.config)

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
	})
}

// 处理日志API
func (ws *WebServer) handleLogs(w http.ResponseWriter, r *http.Request) {
	logs := ws.getLogs()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"logs": logs,
	})
}

// Run 运行Web服务器
func (ws *WebServer) Run(port int) {
	http.HandleFunc("/", ws.handleHome)
	http.HandleFunc("/api/login", ws.handleLogin)
	http.HandleFunc("/api/logout", ws.handleLogout)
	http.HandleFunc("/api/status", ws.handleStatus)
	http.HandleFunc("/api/send", ws.handleSend)
	http.HandleFunc("/api/autoreply", ws.handleAutoReply)
	http.HandleFunc("/api/logs", ws.handleLogs)

	addr := fmt.Sprintf(":%d", port)
	fmt.Printf("🌐 Web控制台已启动: http://localhost%s\n", addr)
	fmt.Println("请在浏览器中打开上面的地址使用图形界面")

	log.Fatal(http.ListenAndServe(addr, nil))
}

func main() {
	server := NewWebServer()
	server.Run(8080)
}
