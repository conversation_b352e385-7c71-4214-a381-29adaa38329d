package ui

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"wechat-bot-gui/bot"
	"wechat-bot-gui/config"
)

// SimpleWindow 简化版主窗口
type SimpleWindow struct {
	app    fyne.App
	window fyne.Window
	bot    *bot.WeChatBot
	config *config.Config

	// UI组件
	statusLabel    *widget.Label
	loginBtn       *widget.Button
	logoutBtn      *widget.Button
	targetEntry    *widget.Entry
	messageEntry   *widget.Entry
	sendBtn        *widget.Button
	autoReplyCheck *widget.Check
	logDisplay     *widget.Entry

	// 数据
	logs []string
}

// NewSimpleWindow 创建简化版主窗口
func NewSimpleWindow() *SimpleWindow {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		cfg = config.DefaultConfig()
	}

	// 创建应用
	myApp := app.NewWithID("com.wechat.bot.simple")
	myWindow := myApp.NewWindow("微信推送机器人 - 简易版")
	myWindow.Resize(fyne.NewSize(600, 500))
	myWindow.CenterOnScreen()

	// 创建机器人实例
	wechatBot := bot.NewWeChatBot(cfg)

	sw := &SimpleWindow{
		app:    myApp,
		window: myWindow,
		bot:    wechatBot,
		config: cfg,
		logs:   []string{},
	}

	// 设置机器人回调
	sw.setupBotCallbacks()

	// 创建UI
	sw.createSimpleUI()

	return sw
}

// setupBotCallbacks 设置机器人回调函数
func (sw *SimpleWindow) setupBotCallbacks() {
	sw.bot.OnLoginSuccess = func() {
		sw.statusLabel.SetText("✅ 状态: 已登录")
		sw.statusLabel.Importance = widget.SuccessImportance
		sw.loginBtn.Disable()
		sw.logoutBtn.Enable()
		sw.sendBtn.Enable()
		sw.addLog("登录成功！现在可以发送消息了。")
	}

	sw.bot.OnLoginFailed = func(err error) {
		sw.statusLabel.SetText("❌ 状态: 登录失败")
		sw.statusLabel.Importance = widget.DangerImportance
		sw.addLog(fmt.Sprintf("登录失败: %v", err))
		dialog.ShowError(err, sw.window)
	}

	sw.bot.OnLogout = func() {
		sw.statusLabel.SetText("⭕ 状态: 未登录")
		sw.statusLabel.Importance = widget.MediumImportance
		sw.loginBtn.Enable()
		sw.logoutBtn.Disable()
		sw.sendBtn.Disable()
		sw.addLog("已退出登录")
	}

	sw.bot.OnMessage = func(message string) {
		sw.addLog(message)
	}

	sw.bot.OnQRCode = func(qrURL string) {
		sw.showSimpleQRDialog(qrURL)
	}
}

// createSimpleUI 创建简化的用户界面
func (sw *SimpleWindow) createSimpleUI() {
	// 状态和登录区域
	sw.statusLabel = widget.NewLabel("⭕ 状态: 未登录")
	sw.statusLabel.Importance = widget.MediumImportance

	sw.loginBtn = widget.NewButton("🔑 登录微信", sw.onLogin)
	sw.loginBtn.Importance = widget.HighImportance

	sw.logoutBtn = widget.NewButton("🚪 退出登录", sw.onLogout)
	sw.logoutBtn.Disable()

	loginArea := container.NewHBox(
		sw.statusLabel,
		widget.NewSeparator(),
		sw.loginBtn,
		sw.logoutBtn,
	)

	// 消息发送区域
	sw.targetEntry = widget.NewEntry()
	sw.targetEntry.SetPlaceHolder("输入好友昵称或群名称...")

	sw.messageEntry = widget.NewMultiLineEntry()
	sw.messageEntry.SetPlaceHolder("输入要发送的消息内容...")
	sw.messageEntry.Resize(fyne.NewSize(0, 80))

	sw.sendBtn = widget.NewButton("📤 发送消息", sw.onSendMessage)
	sw.sendBtn.Importance = widget.HighImportance
	sw.sendBtn.Disable()

	messageArea := container.NewVBox(
		widget.NewLabel("📝 消息发送"),
		widget.NewLabel("发送目标:"),
		sw.targetEntry,
		widget.NewLabel("消息内容:"),
		sw.messageEntry,
		sw.sendBtn,
	)

	// 设置区域
	sw.autoReplyCheck = widget.NewCheck("🤖 启用自动回复", sw.onAutoReplyToggle)
	sw.autoReplyCheck.SetChecked(sw.config.AutoReply.Enabled)

	helpBtn := widget.NewButton("❓ 使用帮助", sw.showHelp)

	settingsArea := container.NewHBox(
		sw.autoReplyCheck,
		widget.NewSeparator(),
		helpBtn,
	)

	// 日志区域
	sw.logDisplay = widget.NewMultiLineEntry()
	sw.logDisplay.Disable()
	sw.logDisplay.SetText("欢迎使用微信推送机器人！\n\n使用步骤:\n1. 点击\"登录微信\"按钮\n2. 扫描弹出的二维码\n3. 登录成功后即可发送消息\n\n提示: 发送目标请输入好友的昵称或群名称")

	clearLogBtn := widget.NewButton("🗑️ 清空日志", func() {
		sw.logs = []string{}
		sw.updateLogDisplay()
	})

	logArea := container.NewVBox(
		widget.NewLabel("📋 运行日志"),
		container.NewScroll(sw.logDisplay),
		clearLogBtn,
	)

	// 主布局
	content := container.NewVBox(
		loginArea,
		widget.NewSeparator(),
		messageArea,
		widget.NewSeparator(),
		settingsArea,
		widget.NewSeparator(),
		logArea,
	)

	sw.window.SetContent(container.NewPadded(content))
}

// onLogin 登录按钮点击事件
func (sw *SimpleWindow) onLogin() {
	sw.statusLabel.SetText("🔄 状态: 正在登录...")
	sw.loginBtn.Disable()
	sw.addLog("开始登录流程，请稍候...")

	go func() {
		if err := sw.bot.Login(); err != nil {
			sw.statusLabel.SetText("❌ 状态: 登录失败")
			sw.loginBtn.Enable()
		}
	}()
}

// onLogout 退出登录按钮点击事件
func (sw *SimpleWindow) onLogout() {
	sw.bot.Logout()
}

// onSendMessage 发送消息按钮点击事件
func (sw *SimpleWindow) onSendMessage() {
	target := strings.TrimSpace(sw.targetEntry.Text)
	message := strings.TrimSpace(sw.messageEntry.Text)

	if target == "" {
		dialog.ShowInformation("提示", "请输入发送目标（好友昵称或群名称）", sw.window)
		return
	}

	if message == "" {
		dialog.ShowInformation("提示", "请输入要发送的消息内容", sw.window)
		return
	}

	sw.addLog(fmt.Sprintf("正在发送消息给: %s", target))

	go func() {
		// 先尝试发送给好友
		err := sw.bot.SendTextToFriend(target, message)
		if err != nil {
			// 如果发送给好友失败，尝试发送给群组
			err = sw.bot.SendTextToGroup(target, message)
		}

		if err != nil {
			sw.addLog(fmt.Sprintf("发送失败: %v", err))
			dialog.ShowError(fmt.Errorf("发送失败: %v\n\n请检查:\n1. 目标名称是否正确\n2. 是否为该联系人的好友\n3. 群组名称是否准确", err), sw.window)
		} else {
			sw.messageEntry.SetText("")
			sw.addLog("消息发送成功！")
			dialog.ShowInformation("成功", "消息发送成功！", sw.window)
		}
	}()
}

// onAutoReplyToggle 自动回复开关事件
func (sw *SimpleWindow) onAutoReplyToggle(checked bool) {
	sw.config.AutoReply.Enabled = checked
	config.SaveConfig(sw.config)

	if checked {
		sw.addLog("自动回复已启用")
	} else {
		sw.addLog("自动回复已禁用")
	}
}

// showHelp 显示帮助信息
func (sw *SimpleWindow) showHelp() {
	helpText := `📖 使用帮助

🔑 登录步骤:
1. 点击"登录微信"按钮
2. 扫描弹出的二维码
3. 在手机微信上确认登录

📤 发送消息:
1. 在"发送目标"中输入好友昵称或群名称
2. 在"消息内容"中输入要发送的内容
3. 点击"发送消息"按钮

🤖 自动回复:
勾选后，机器人会根据预设关键词自动回复消息

⚠️ 注意事项:
• 请确保网络连接稳定
• 发送目标名称要完全匹配
• 避免频繁发送消息
• 遵守微信使用规范`

	dialog.ShowInformation("使用帮助", helpText, sw.window)
}

// showSimpleQRDialog 显示简化的二维码对话框
func (sw *SimpleWindow) showSimpleQRDialog(qrURL string) {
	sw.addLog("请扫描二维码登录微信")

	// 创建二维码显示对话框
	qrLabel := widget.NewLabel("📱 请使用微信扫描二维码登录")
	qrLabel.Alignment = fyne.TextAlignCenter

	// 创建可点击的链接
	qrLink := widget.NewHyperlink("🔗 点击打开二维码", nil)
	if parsedURL, err := url.Parse(qrURL); err == nil {
		qrLink.SetURL(parsedURL)
	}
	qrLink.Alignment = fyne.TextAlignCenter

	// 提示信息
	tipLabel := widget.NewLabel("💡 提示: 点击上方链接在浏览器中打开二维码")
	tipLabel.Alignment = fyne.TextAlignCenter
	tipLabel.Wrapping = fyne.TextWrapWord

	content := container.NewVBox(
		qrLabel,
		widget.NewSeparator(),
		qrLink,
		widget.NewSeparator(),
		tipLabel,
	)

	qrDialog := dialog.NewCustom("微信登录", "关闭", content, sw.window)
	qrDialog.Resize(fyne.NewSize(350, 200))
	qrDialog.Show()
}

// addLog 添加日志消息
func (sw *SimpleWindow) addLog(message string) {
	now := time.Now()
	timestamp := fmt.Sprintf("[%02d:%02d:%02d]", now.Hour(), now.Minute(), now.Second())

	logMsg := fmt.Sprintf("%s %s", timestamp, message)
	sw.logs = append(sw.logs, logMsg)

	// 保持日志数量在合理范围内
	if len(sw.logs) > 100 {
		sw.logs = sw.logs[10:]
	}

	sw.updateLogDisplay()
}

// updateLogDisplay 更新日志显示
func (sw *SimpleWindow) updateLogDisplay() {
	logContent := strings.Join(sw.logs, "\n")
	sw.logDisplay.SetText(logContent)

	// 滚动到底部
	sw.logDisplay.CursorRow = len(sw.logs)
	sw.logDisplay.Refresh()
}

// Run 运行应用
func (sw *SimpleWindow) Run() {
	sw.window.ShowAndRun()
}

// Close 关闭应用
func (sw *SimpleWindow) Close() {
	if sw.bot.IsLoggedIn() {
		sw.bot.Logout()
	}
	sw.app.Quit()
}
