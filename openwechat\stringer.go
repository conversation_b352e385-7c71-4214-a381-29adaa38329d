// Code generated by "stringer -type=MessageType,Ret,AppMessageType -linecomment=true -output=stringer.go"; DO NOT EDIT.

package openwechat

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[MsgTypeText-1]
	_ = x[MsgTypeImage-3]
	_ = x[MsgTypeVoice-34]
	_ = x[MsgTypeVerify-37]
	_ = x[MsgTypePossibleFriend-40]
	_ = x[MsgTypeShareCard-42]
	_ = x[MsgTypeVideo-43]
	_ = x[MsgTypeEmoticon-47]
	_ = x[MsgTypeLocation-48]
	_ = x[MsgTypeApp-49]
	_ = x[MsgTypeVoip-50]
	_ = x[MsgTypeVoipNotify-52]
	_ = x[MsgTypeVoipInvite-53]
	_ = x[MsgTypeMicroVideo-62]
	_ = x[MsgTypeSys-10000]
	_ = x[MsgTypeRecalled-10002]
}

const _MessageType_name = "文本消息图片消息语音消息认证消息好友推荐消息名片消息视频消息表情消息地理位置消息APP消息VOIP消息VOIP结束消息VOIP邀请小视频消息系统消息消息撤回"

var _MessageType_map = map[MessageType]string{
	1:     _MessageType_name[0:12],
	3:     _MessageType_name[12:24],
	34:    _MessageType_name[24:36],
	37:    _MessageType_name[36:48],
	40:    _MessageType_name[48:66],
	42:    _MessageType_name[66:78],
	43:    _MessageType_name[78:90],
	47:    _MessageType_name[90:102],
	48:    _MessageType_name[102:120],
	49:    _MessageType_name[120:129],
	50:    _MessageType_name[129:139],
	52:    _MessageType_name[139:155],
	53:    _MessageType_name[155:165],
	62:    _MessageType_name[165:180],
	10000: _MessageType_name[180:192],
	10002: _MessageType_name[192:204],
}

func (i MessageType) String() string {
	if str, ok := _MessageType_map[i]; ok {
		return str
	}
	return "MessageType(" + strconv.FormatInt(int64(i), 10) + ")"
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ticketError - -14]
	_ = x[logicError - -2]
	_ = x[sysError - -1]
	_ = x[paramError-1]
	_ = x[failedLoginWarn-1100]
	_ = x[failedLoginCheck-1101]
	_ = x[cookieInvalid-1102]
	_ = x[loginEnvAbnormality-1203]
	_ = x[optTooOften-1205]
}

const (
	_Ret_name_0 = "ticket error"
	_Ret_name_1 = "logic errorsys error"
	_Ret_name_2 = "param error"
	_Ret_name_3 = "failed login warnfailed login checkcookie invalid"
	_Ret_name_4 = "login environmental abnormality"
	_Ret_name_5 = "operate too often"
)

var (
	_Ret_index_1 = [...]uint8{0, 11, 20}
	_Ret_index_3 = [...]uint8{0, 17, 35, 49}
)

func (i Ret) String() string {
	switch {
	case i == -14:
		return _Ret_name_0
	case -2 <= i && i <= -1:
		i -= -2
		return _Ret_name_1[_Ret_index_1[i]:_Ret_index_1[i+1]]
	case i == 1:
		return _Ret_name_2
	case 1100 <= i && i <= 1102:
		i -= 1100
		return _Ret_name_3[_Ret_index_3[i]:_Ret_index_3[i+1]]
	case i == 1203:
		return _Ret_name_4
	case i == 1205:
		return _Ret_name_5
	default:
		return "Ret(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[AppMsgTypeText-1]
	_ = x[AppMsgTypeImg-2]
	_ = x[AppMsgTypeAudio-3]
	_ = x[AppMsgTypeVideo-4]
	_ = x[AppMsgTypeUrl-5]
	_ = x[AppMsgTypeAttach-6]
	_ = x[AppMsgTypeOpen-7]
	_ = x[AppMsgTypeEmoji-8]
	_ = x[AppMsgTypeVoiceRemind-9]
	_ = x[AppMsgTypeScanGood-10]
	_ = x[AppMsgTypeGood-13]
	_ = x[AppMsgTypeEmotion-15]
	_ = x[AppMsgTypeCardTicket-16]
	_ = x[AppMsgTypeRealtimeShareLocation-17]
	_ = x[AppMsgTypeTransfers-2000]
	_ = x[AppMsgTypeRedEnvelopes-2001]
	_ = x[AppMsgTypeReaderType-100001]
}

const (
	_AppMessageType_name_0 = "文本消息图片消息语音消息视频消息文章消息附件消息Open表情消息VoiceRemindScanGood"
	_AppMessageType_name_1 = "Good"
	_AppMessageType_name_2 = "Emotion名片消息地理位置消息"
	_AppMessageType_name_3 = "转账消息红包消息"
	_AppMessageType_name_4 = "自定义的消息"
)

var (
	_AppMessageType_index_0 = [...]uint8{0, 12, 24, 36, 48, 60, 72, 76, 88, 99, 107}
	_AppMessageType_index_2 = [...]uint8{0, 7, 19, 37}
	_AppMessageType_index_3 = [...]uint8{0, 12, 24}
)

func (i AppMessageType) String() string {
	switch {
	case 1 <= i && i <= 10:
		i -= 1
		return _AppMessageType_name_0[_AppMessageType_index_0[i]:_AppMessageType_index_0[i+1]]
	case i == 13:
		return _AppMessageType_name_1
	case 15 <= i && i <= 17:
		i -= 15
		return _AppMessageType_name_2[_AppMessageType_index_2[i]:_AppMessageType_index_2[i+1]]
	case 2000 <= i && i <= 2001:
		i -= 2000
		return _AppMessageType_name_3[_AppMessageType_index_3[i]:_AppMessageType_index_3[i+1]]
	case i == 100001:
		return _AppMessageType_name_4
	default:
		return "AppMessageType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
