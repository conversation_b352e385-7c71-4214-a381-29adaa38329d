@echo off
chcp 65001 >nul 2>&1
title WeChat Bot Launcher

:main
cls
echo.
echo ========================================
echo    WeChat Push Bot - Smart Launcher
echo ========================================
echo.
echo Please select startup method:
echo.
echo  1. Console Version (Recommended for beginners)
echo     - Simple to use, interactive command operation
echo     - No browser needed, use directly in console
echo.
echo  2. Web Interface Version (Recommended for novices)
echo     - Beautiful web interface, most user-friendly
echo     - Operate in browser, supports real-time updates
echo.
echo  3. Compile and Run (For developers)
echo     - Compile and run from source code
echo     - Suitable for development and debugging
echo.
echo  4. View Usage Instructions
echo.
echo  5. Exit
echo.
set /p choice="Please enter your choice (1-5): "

if "%choice%"=="1" goto console
if "%choice%"=="2" goto web
if "%choice%"=="3" goto compile
if "%choice%"=="4" goto help
if "%choice%"=="5" goto exit
echo Invalid choice, please try again
pause
goto main

:console
cls
echo ========================================
echo    Console Version Startup
echo ========================================
echo.

if exist "console.exe" (
    echo [INFO] Found executable file, running directly...
    echo [TIP] After startup, type 'help' to see available commands
    echo [TIP] Type 'quit' to exit the program
    echo.
    pause
    console.exe
) else (
    echo [INFO] Executable not found, trying to run from source...
    call :check_go
    if errorlevel 1 goto main
    echo [INFO] Running console version from source...
    echo.
    go run cmd/console.go
)

echo.
echo [INFO] Program exited
pause
goto main

:web
cls
echo ========================================
echo    Web Interface Version Startup
echo ========================================
echo.

if exist "web-gui.exe" (
    echo [INFO] Found executable file, running directly...
    echo [TIP] After startup, the URL will be displayed, please open it in browser
    echo [TIP] Default address: http://localhost:8080
    echo [TIP] Press Ctrl+C to stop the server
    echo.
    pause
    web-gui.exe
) else (
    echo [INFO] Executable not found, trying to run from source...
    call :check_go
    if errorlevel 1 goto main
    echo [INFO] Running web version from source...
    echo.
    go run web/server.go
)

echo.
echo [INFO] Web server stopped
pause
goto main

:compile
cls
echo ========================================
echo    Compile and Run
echo ========================================
echo.

call :check_go
if errorlevel 1 goto main

echo [INFO] Installing dependencies...
go mod tidy
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    pause
    goto main
)

echo [INFO] Compiling console version...
go build -o console.exe cmd/console.go
if errorlevel 1 (
    echo [ERROR] Failed to compile console version
) else (
    echo [SUCCESS] Console version compiled: console.exe
)

echo [INFO] Compiling web version...
go build -o web-gui.exe web/server.go
if errorlevel 1 (
    echo [ERROR] Failed to compile web version
) else (
    echo [SUCCESS] Web version compiled: web-gui.exe
)

echo.
echo [INFO] Compilation completed! You can now choose a running method.
pause
goto main

:help
cls
echo ========================================
echo    Usage Instructions
echo ========================================
echo.
echo Quick Start:
echo   1. Choose a startup method (Web interface version recommended)
echo   2. Click "Login WeChat" after program starts
echo   3. Scan QR code to complete login
echo   4. Enter friend nickname or group name to send messages
echo.
echo Version Comparison:
echo   - Console version: Suitable for users familiar with command line
echo   - Web interface version: Most user-friendly, recommended for novices
echo.
echo Requirements:
echo   - Go 1.20 or higher
echo   - Stable network connection
echo   - WeChat account that supports web login
echo.
echo For detailed instructions, see: README.md
echo.
pause
goto main

:check_go
echo [INFO] Checking Go environment...
go version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Go environment not detected, please install Go first
    echo [TIP] Download from: https://golang.org/dl/
    echo.
    pause
    exit /b 1
)
echo [INFO] Go environment check passed
exit /b 0

:exit
echo.
echo Thank you for using WeChat Push Bot!
echo.
exit /b 0
