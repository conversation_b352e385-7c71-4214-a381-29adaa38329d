#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "    微信推送机器人GUI控制台"
echo -e "========================================${NC}"
echo

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}[错误] 未检测到Go环境，请先安装Go语言环境${NC}"
    echo -e "${YELLOW}下载地址: https://golang.org/dl/${NC}"
    echo
    exit 1
fi

echo -e "${GREEN}[信息] Go环境检测正常${NC}"
echo

# 检查依赖
echo -e "${YELLOW}[信息] 正在检查和安装依赖...${NC}"
if ! go mod tidy; then
    echo -e "${RED}[错误] 依赖安装失败${NC}"
    exit 1
fi

echo -e "${GREEN}[信息] 依赖安装完成${NC}"
echo

# 运行程序
echo -e "${YELLOW}[信息] 正在启动微信推送机器人...${NC}"
echo -e "${BLUE}[提示] 程序启动后会打开图形界面窗口${NC}"
echo -e "${BLUE}[提示] 如需退出，请关闭图形界面窗口或按Ctrl+C${NC}"
echo

go run main.go

echo
echo -e "${GREEN}[信息] 程序已退出${NC}"
