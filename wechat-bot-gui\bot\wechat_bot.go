package bot

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/eatmoreapple/openwechat"
	"wechat-bot-gui/config"
)

// WeChatBot 微信机器人结构
type WeChatBot struct {
	bot        *openwechat.Bot
	self       *openwechat.Self
	config     *config.Config
	isLoggedIn bool
	mutex      sync.RWMutex
	
	// 回调函数
	OnLoginSuccess func()
	OnLoginFailed  func(error)
	OnLogout       func()
	OnMessage      func(string) // 日志消息回调
	OnQRCode       func(string) // 二维码回调
}

// NewWeChatBot 创建新的微信机器人
func NewWeChatBot(cfg *config.Config) *WeChatBot {
	bot := &WeChatBot{
		config:     cfg,
		isLoggedIn: false,
	}
	
	// 创建openwechat机器人实例
	bot.bot = openwechat.DefaultBot(openwechat.Desktop)
	
	// 设置消息处理器
	bot.bot.MessageHandler = bot.handleMessage
	
	// 设置二维码回调
	bot.bot.UUIDCallback = bot.handleQRCode
	
	return bot
}

// IsLoggedIn 检查是否已登录
func (w *WeChatBot) IsLoggedIn() bool {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	return w.isLoggedIn
}

// Login 登录微信
func (w *WeChatBot) Login() error {
	w.logMessage("开始登录微信...")
	
	// 登录
	if err := w.bot.Login(); err != nil {
		w.logMessage(fmt.Sprintf("登录失败: %v", err))
		if w.OnLoginFailed != nil {
			w.OnLoginFailed(err)
		}
		return err
	}

	// 获取当前用户信息
	self, err := w.bot.GetCurrentUser()
	if err != nil {
		w.logMessage(fmt.Sprintf("获取用户信息失败: %v", err))
		if w.OnLoginFailed != nil {
			w.OnLoginFailed(err)
		}
		return err
	}

	w.mutex.Lock()
	w.self = self
	w.isLoggedIn = true
	w.mutex.Unlock()

	w.logMessage(fmt.Sprintf("登录成功! 用户: %s", self.NickName))
	
	if w.OnLoginSuccess != nil {
		w.OnLoginSuccess()
	}

	return nil
}

// Logout 退出登录
func (w *WeChatBot) Logout() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	if w.bot != nil {
		w.bot.Logout()
	}
	
	w.isLoggedIn = false
	w.self = nil
	
	w.logMessage("已退出登录")
	
	if w.OnLogout != nil {
		w.OnLogout()
	}
	
	return nil
}

// GetFriends 获取好友列表
func (w *WeChatBot) GetFriends() ([]*openwechat.Friend, error) {
	if !w.IsLoggedIn() {
		return nil, fmt.Errorf("未登录")
	}
	
	return w.self.Friends()
}

// GetGroups 获取群组列表
func (w *WeChatBot) GetGroups() ([]*openwechat.Group, error) {
	if !w.IsLoggedIn() {
		return nil, fmt.Errorf("未登录")
	}
	
	return w.self.Groups()
}

// SendTextToFriend 发送文本消息给好友
func (w *WeChatBot) SendTextToFriend(friendName, text string) error {
	if !w.IsLoggedIn() {
		return fmt.Errorf("未登录")
	}

	friends, err := w.GetFriends()
	if err != nil {
		return err
	}

	for _, friend := range friends {
		if friend.NickName == friendName || friend.RemarkName == friendName {
			_, err := friend.SendText(text)
			if err == nil {
				w.logMessage(fmt.Sprintf("已发送消息给 %s: %s", friendName, text))
			}
			return err
		}
	}

	return fmt.Errorf("未找到好友: %s", friendName)
}

// SendTextToGroup 发送文本消息给群组
func (w *WeChatBot) SendTextToGroup(groupName, text string) error {
	if !w.IsLoggedIn() {
		return fmt.Errorf("未登录")
	}

	groups, err := w.GetGroups()
	if err != nil {
		return err
	}

	for _, group := range groups {
		if group.NickName == groupName {
			_, err := group.SendText(text)
			if err == nil {
				w.logMessage(fmt.Sprintf("已发送消息给群组 %s: %s", groupName, text))
			}
			return err
		}
	}

	return fmt.Errorf("未找到群组: %s", groupName)
}

// handleMessage 处理接收到的消息
func (w *WeChatBot) handleMessage(msg *openwechat.Message) {
	// 记录收到的消息
	sender := msg.FromUserName
	if msg.IsSendByFriend() {
		friend, _ := msg.Sender()
		if friend != nil {
			sender = friend.NickName
		}
	} else if msg.IsSendByGroup() {
		group, _ := msg.SenderInGroup()
		if group != nil {
			sender = group.NickName
		}
	}
	
	w.logMessage(fmt.Sprintf("收到消息 [%s]: %s", sender, msg.Content))

	// 自动回复处理
	if w.config.AutoReply.Enabled && msg.IsText() {
		w.handleAutoReply(msg)
	}
}

// handleAutoReply 处理自动回复
func (w *WeChatBot) handleAutoReply(msg *openwechat.Message) {
	content := strings.TrimSpace(msg.Content)
	
	// 检查关键词匹配
	for keyword, reply := range w.config.AutoReply.Keywords {
		if strings.Contains(content, keyword) {
			// 延迟回复，模拟人工操作
			go func() {
				time.Sleep(time.Second * 2)
				msg.ReplyText(reply)
				w.logMessage(fmt.Sprintf("自动回复: %s", reply))
			}()
			break
		}
	}
}

// handleQRCode 处理二维码
func (w *WeChatBot) handleQRCode(uuid string) {
	qrURL := "https://login.weixin.qq.com/qrcode/" + uuid
	w.logMessage("请扫描二维码登录: " + qrURL)
	
	if w.OnQRCode != nil {
		w.OnQRCode(qrURL)
	}
}

// logMessage 记录日志消息
func (w *WeChatBot) logMessage(message string) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMsg := fmt.Sprintf("[%s] %s", timestamp, message)
	
	// 输出到控制台
	log.Println(logMsg)
	
	// 回调到UI
	if w.OnMessage != nil {
		w.OnMessage(logMsg)
	}
}

// Block 阻塞等待
func (w *WeChatBot) Block() {
	if w.bot != nil {
		w.bot.Block()
	}
}
