package ui

import (
	"net/url"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"wechat-bot-gui/bot"
	"wechat-bot-gui/config"
)

// MainWindow 主窗口结构
type MainWindow struct {
	app    fyne.App
	window fyne.Window
	bot    *bot.WeChatBot
	config *config.Config

	// UI组件
	statusLabel    *widget.Label
	loginBtn       *widget.Button
	logoutBtn      *widget.Button
	friendsList    *widget.List
	groupsList     *widget.List
	logText        *widget.Entry
	messageEntry   *widget.Entry
	targetSelect   *widget.Select
	autoReplyCheck *widget.Check

	// 数据
	friends []string
	groups  []string
	logs    []string
}

// NewMainWindow 创建主窗口
func NewMainWindow() *MainWindow {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		cfg = config.DefaultConfig()
	}

	// 创建应用
	myApp := app.NewWithID("com.wechat.bot.gui")
	myApp.SetIcon(theme.ComputerIcon())

	// 创建窗口
	myWindow := myApp.NewWindow("微信推送机器人控制台")
	myWindow.Resize(fyne.NewSize(1000, 700))
	myWindow.CenterOnScreen()

	// 创建机器人实例
	wechatBot := bot.NewWeChatBot(cfg)

	mw := &MainWindow{
		app:     myApp,
		window:  myWindow,
		bot:     wechatBot,
		config:  cfg,
		friends: []string{},
		groups:  []string{},
		logs:    []string{},
	}

	// 设置机器人回调
	mw.setupBotCallbacks()

	// 创建UI
	mw.createUI()

	return mw
}

// setupBotCallbacks 设置机器人回调函数
func (mw *MainWindow) setupBotCallbacks() {
	mw.bot.OnLoginSuccess = func() {
		mw.statusLabel.SetText("状态: 已登录")
		mw.loginBtn.Disable()
		mw.logoutBtn.Enable()
		mw.refreshContacts()
	}

	mw.bot.OnLoginFailed = func(err error) {
		mw.statusLabel.SetText("状态: 登录失败")
		dialog.ShowError(err, mw.window)
	}

	mw.bot.OnLogout = func() {
		mw.statusLabel.SetText("状态: 未登录")
		mw.loginBtn.Enable()
		mw.logoutBtn.Disable()
		mw.friends = []string{}
		mw.groups = []string{}
		mw.friendsList.Refresh()
		mw.groupsList.Refresh()
		mw.targetSelect.Options = []string{}
		mw.targetSelect.Refresh()
	}

	mw.bot.OnMessage = func(message string) {
		mw.logs = append(mw.logs, message)
		// 保持日志数量在合理范围内
		if len(mw.logs) > 1000 {
			mw.logs = mw.logs[100:]
		}
		mw.updateLogDisplay()
	}

	mw.bot.OnQRCode = func(qrURL string) {
		// 显示二维码对话框
		mw.showQRCodeDialog(qrURL)
	}
}

// createUI 创建用户界面
func (mw *MainWindow) createUI() {
	// 顶部状态栏
	mw.statusLabel = widget.NewLabel("状态: 未登录")
	mw.statusLabel.TextStyle = fyne.TextStyle{Bold: true}

	mw.loginBtn = widget.NewButton("登录微信", mw.onLogin)
	mw.loginBtn.Importance = widget.HighImportance

	mw.logoutBtn = widget.NewButton("退出登录", mw.onLogout)
	mw.logoutBtn.Disable()

	statusBar := container.NewHBox(
		mw.statusLabel,
		layout.NewSpacer(),
		mw.loginBtn,
		mw.logoutBtn,
	)

	// 左侧联系人面板
	friendsLabel := widget.NewLabel("好友列表")
	friendsLabel.TextStyle = fyne.TextStyle{Bold: true}

	mw.friendsList = widget.NewList(
		func() int { return len(mw.friends) },
		func() fyne.CanvasObject {
			return widget.NewLabel("好友")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(mw.friends[id])
		},
	)

	groupsLabel := widget.NewLabel("群组列表")
	groupsLabel.TextStyle = fyne.TextStyle{Bold: true}

	mw.groupsList = widget.NewList(
		func() int { return len(mw.groups) },
		func() fyne.CanvasObject {
			return widget.NewLabel("群组")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(mw.groups[id])
		},
	)

	contactsPanel := container.NewVBox(
		friendsLabel,
		container.NewScroll(mw.friendsList),
		groupsLabel,
		container.NewScroll(mw.groupsList),
	)

	// 中间消息发送面板
	mw.targetSelect = widget.NewSelect([]string{}, nil)
	mw.targetSelect.PlaceHolder = "选择发送目标..."

	mw.messageEntry = widget.NewMultiLineEntry()
	mw.messageEntry.SetPlaceHolder("输入要发送的消息...")
	mw.messageEntry.Resize(fyne.NewSize(400, 100))

	sendBtn := widget.NewButton("发送消息", mw.onSendMessage)
	sendBtn.Importance = widget.HighImportance

	messagePanel := container.NewVBox(
		widget.NewLabel("消息发送"),
		mw.targetSelect,
		mw.messageEntry,
		sendBtn,
	)

	// 右侧设置和日志面板
	mw.autoReplyCheck = widget.NewCheck("启用自动回复", mw.onAutoReplyToggle)
	mw.autoReplyCheck.SetChecked(mw.config.AutoReply.Enabled)

	configBtn := widget.NewButton("高级设置", mw.onShowConfig)

	mw.logText = widget.NewMultiLineEntry()
	mw.logText.Disable()
	mw.logText.SetText("欢迎使用微信推送机器人控制台！\n请点击\"登录微信\"开始使用。")

	clearLogBtn := widget.NewButton("清空日志", func() {
		mw.logs = []string{}
		mw.updateLogDisplay()
	})

	settingsPanel := container.NewVBox(
		widget.NewLabel("设置"),
		mw.autoReplyCheck,
		configBtn,
		widget.NewSeparator(),
		widget.NewLabel("运行日志"),
		container.NewScroll(mw.logText),
		clearLogBtn,
	)

	// 主布局
	leftPanel := container.NewBorder(nil, nil, nil, nil, contactsPanel)
	centerPanel := container.NewBorder(nil, nil, nil, nil, messagePanel)
	rightPanel := container.NewBorder(nil, nil, nil, nil, settingsPanel)

	mainContent := container.NewHSplit(
		container.NewHSplit(leftPanel, centerPanel),
		rightPanel,
	)
	mainContent.SetOffset(0.3)

	content := container.NewBorder(statusBar, nil, nil, nil, mainContent)
	mw.window.SetContent(content)
}

// onLogin 登录按钮点击事件
func (mw *MainWindow) onLogin() {
	mw.statusLabel.SetText("状态: 正在登录...")
	mw.loginBtn.Disable()

	go func() {
		if err := mw.bot.Login(); err != nil {
			mw.statusLabel.SetText("状态: 登录失败")
			mw.loginBtn.Enable()
		}
	}()
}

// onLogout 退出登录按钮点击事件
func (mw *MainWindow) onLogout() {
	mw.bot.Logout()
}

// onSendMessage 发送消息按钮点击事件
func (mw *MainWindow) onSendMessage() {
	target := mw.targetSelect.Selected
	message := strings.TrimSpace(mw.messageEntry.Text)

	if target == "" {
		dialog.ShowInformation("提示", "请选择发送目标", mw.window)
		return
	}

	if message == "" {
		dialog.ShowInformation("提示", "请输入要发送的消息", mw.window)
		return
	}

	go func() {
		var err error
		if strings.HasPrefix(target, "[好友] ") {
			friendName := strings.TrimPrefix(target, "[好友] ")
			err = mw.bot.SendTextToFriend(friendName, message)
		} else if strings.HasPrefix(target, "[群组] ") {
			groupName := strings.TrimPrefix(target, "[群组] ")
			err = mw.bot.SendTextToGroup(groupName, message)
		}

		if err != nil {
			dialog.ShowError(err, mw.window)
		} else {
			mw.messageEntry.SetText("")
		}
	}()
}

// onAutoReplyToggle 自动回复开关事件
func (mw *MainWindow) onAutoReplyToggle(checked bool) {
	mw.config.AutoReply.Enabled = checked
	config.SaveConfig(mw.config)
}

// onShowConfig 显示高级设置对话框
func (mw *MainWindow) onShowConfig() {
	dialog.ShowInformation("高级设置", "高级设置功能开发中...", mw.window)
}

// refreshContacts 刷新联系人列表
func (mw *MainWindow) refreshContacts() {
	go func() {
		// 获取好友列表
		if friends, err := mw.bot.GetFriends(); err == nil {
			mw.friends = []string{}
			for _, friend := range friends {
				name := friend.NickName
				if friend.RemarkName != "" {
					name = friend.RemarkName
				}
				mw.friends = append(mw.friends, name)
			}
			mw.friendsList.Refresh()
		}

		// 获取群组列表
		if groups, err := mw.bot.GetGroups(); err == nil {
			mw.groups = []string{}
			for _, group := range groups {
				mw.groups = append(mw.groups, group.NickName)
			}
			mw.groupsList.Refresh()
		}

		// 更新发送目标选择器
		mw.updateTargetSelect()
	}()
}

// updateTargetSelect 更新发送目标选择器
func (mw *MainWindow) updateTargetSelect() {
	options := []string{}

	// 添加好友
	for _, friend := range mw.friends {
		options = append(options, "[好友] "+friend)
	}

	// 添加群组
	for _, group := range mw.groups {
		options = append(options, "[群组] "+group)
	}

	mw.targetSelect.Options = options
	mw.targetSelect.Refresh()
}

// updateLogDisplay 更新日志显示
func (mw *MainWindow) updateLogDisplay() {
	logContent := strings.Join(mw.logs, "\n")
	mw.logText.SetText(logContent)

	// 滚动到底部
	mw.logText.CursorRow = len(mw.logs)
	mw.logText.Refresh()
}

// showQRCodeDialog 显示二维码对话框
func (mw *MainWindow) showQRCodeDialog(qrURL string) {
	// 创建二维码显示对话框
	qrLabel := widget.NewLabel("请使用微信扫描二维码登录")
	qrLabel.Alignment = fyne.TextAlignCenter

	urlLabel := widget.NewLabel(qrURL)
	urlLabel.Wrapping = fyne.TextWrapWord

	// 创建可点击的链接
	qrLink := widget.NewHyperlink("点击打开二维码", nil)
	if parsedURL, err := url.Parse(qrURL); err == nil {
		qrLink.SetURL(parsedURL)
	}

	content := container.NewVBox(
		qrLabel,
		widget.NewSeparator(),
		qrLink,
		widget.NewSeparator(),
		urlLabel,
	)

	qrDialog := dialog.NewCustom("微信登录", "关闭", content, mw.window)
	qrDialog.Resize(fyne.NewSize(400, 300))
	qrDialog.Show()
}

// Run 运行应用
func (mw *MainWindow) Run() {
	mw.window.ShowAndRun()
}

// Close 关闭应用
func (mw *MainWindow) Close() {
	if mw.bot.IsLoggedIn() {
		mw.bot.Logout()
	}
	mw.app.Quit()
}
