@echo off
chcp 65001 >nul
title 微信推送机器人 - 智能启动器

:main
cls
echo.
echo ========================================
echo    🤖 微信推送机器人 - 智能启动器
echo ========================================
echo.
echo 请选择启动方式:
echo.
echo  1. 💻 命令行版本 (推荐新手)
echo     - 简单易用，交互式命令操作
echo     - 无需浏览器，直接在控制台使用
echo.
echo  2. 🌐 Web界面版本 (推荐小白)
echo     - 美观的网页界面，最用户友好
echo     - 在浏览器中操作，支持实时更新
echo.
echo  3. 🔧 编译并运行 (开发者)
echo     - 从源码编译并运行
echo     - 适合开发和调试
echo.
echo  4. ❓ 查看使用说明
echo.
echo  5. 🚪 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto console
if "%choice%"=="2" goto web
if "%choice%"=="3" goto compile
if "%choice%"=="4" goto help
if "%choice%"=="5" goto exit
echo 无效选择，请重新输入
pause
goto main

:console
cls
echo ========================================
echo    💻 启动命令行版本
echo ========================================
echo.

if exist "console.exe" (
    echo [信息] 找到可执行文件，直接运行...
    echo [提示] 程序启动后，输入 help 查看可用命令
    echo [提示] 输入 quit 退出程序
    echo.
    pause
    console.exe
) else (
    echo [信息] 未找到可执行文件，尝试从源码运行...
    call :check_go
    if errorlevel 1 goto main
    echo [信息] 正在从源码运行命令行版本...
    echo.
    go run cmd/console.go
)

echo.
echo [信息] 程序已退出
pause
goto main

:web
cls
echo ========================================
echo    🌐 启动Web界面版本
echo ========================================
echo.

if exist "web-gui.exe" (
    echo [信息] 找到可执行文件，直接运行...
    echo [提示] 程序启动后会显示网址，请在浏览器中打开
    echo [提示] 默认地址: http://localhost:8080
    echo [提示] 按 Ctrl+C 停止服务器
    echo.
    pause
    web-gui.exe
) else (
    echo [信息] 未找到可执行文件，尝试从源码运行...
    call :check_go
    if errorlevel 1 goto main
    echo [信息] 正在从源码运行Web版本...
    echo.
    go run web/server.go
)

echo.
echo [信息] Web服务器已停止
pause
goto main

:compile
cls
echo ========================================
echo    🔧 编译并运行
echo ========================================
echo.

call :check_go
if errorlevel 1 goto main

echo [信息] 正在安装依赖...
go mod tidy
if errorlevel 1 (
    echo [错误] 依赖安装失败
    pause
    goto main
)

echo [信息] 正在编译命令行版本...
go build -o console.exe cmd/console.go
if errorlevel 1 (
    echo [错误] 命令行版本编译失败
) else (
    echo [成功] 命令行版本编译完成: console.exe
)

echo [信息] 正在编译Web版本...
go build -o web-gui.exe web/server.go
if errorlevel 1 (
    echo [错误] Web版本编译失败
) else (
    echo [成功] Web版本编译完成: web-gui.exe
)

echo.
echo [信息] 编译完成！现在可以选择运行方式。
pause
goto main

:help
cls
echo ========================================
echo    ❓ 使用说明
echo ========================================
echo.
echo 📖 快速开始:
echo   1. 选择一种启动方式（推荐Web界面版本）
echo   2. 程序启动后点击"登录微信"
echo   3. 扫描二维码完成登录
echo   4. 输入好友昵称或群名称发送消息
echo.
echo 💡 版本对比:
echo   • 命令行版本: 适合熟悉命令行的用户
echo   • Web界面版本: 最用户友好，推荐小白使用
echo.
echo 🔧 环境要求:
echo   • Go 1.20 或更高版本
echo   • 稳定的网络连接
echo   • 支持网页版登录的微信账号
echo.
echo 📝 详细说明请查看: 使用说明.md
echo.
pause
goto main

:check_go
echo [信息] 检查Go环境...
go version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Go环境，请先安装Go语言环境
    echo [提示] 下载地址: https://golang.org/dl/
    echo.
    pause
    exit /b 1
)
echo [信息] Go环境检测正常
exit /b 0

:exit
echo.
echo 感谢使用微信推送机器人！
echo.
exit /b 0
