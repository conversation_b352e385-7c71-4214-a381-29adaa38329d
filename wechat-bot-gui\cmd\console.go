package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"

	"wechat-bot-gui/bot"
	"wechat-bot-gui/config"
)

// ConsoleApp 控制台应用
type ConsoleApp struct {
	bot    *bot.WeChatBot
	config *config.Config
	reader *bufio.Reader
}

// NewConsoleApp 创建控制台应用
func NewConsoleApp() *ConsoleApp {
	cfg, err := config.LoadConfig()
	if err != nil {
		cfg = config.DefaultConfig()
	}

	wechatBot := bot.NewWeChatBot(cfg)

	app := &ConsoleApp{
		bot:    wechatBot,
		config: cfg,
		reader: bufio.NewReader(os.Stdin),
	}

	// 设置回调
	app.setupCallbacks()

	return app
}

// setupCallbacks 设置回调函数
func (app *ConsoleApp) setupCallbacks() {
	app.bot.OnLoginSuccess = func() {
		fmt.Println("✅ 登录成功！")
		fmt.Println("现在可以使用以下命令:")
		app.showHelp()
	}

	app.bot.OnLoginFailed = func(err error) {
		fmt.Printf("❌ 登录失败: %v\n", err)
	}

	app.bot.OnLogout = func() {
		fmt.Println("👋 已退出登录")
	}

	app.bot.OnMessage = func(message string) {
		fmt.Println(message)
	}

	app.bot.OnQRCode = func(qrURL string) {
		fmt.Println("📱 请扫描二维码登录:")
		fmt.Println(qrURL)
		fmt.Println("提示: 复制上面的链接到浏览器中打开，然后扫描二维码")
	}
}

// Run 运行控制台应用
func (app *ConsoleApp) Run() {
	fmt.Println("========================================")
	fmt.Println("    微信推送机器人控制台 - 命令行版")
	fmt.Println("========================================")
	fmt.Println()

	for {
		fmt.Print("请输入命令 (输入 help 查看帮助): ")
		input, _ := app.reader.ReadString('\n')
		command := strings.TrimSpace(input)

		if command == "" {
			continue
		}

		if app.handleCommand(command) {
			break
		}
	}
}

// handleCommand 处理命令
func (app *ConsoleApp) handleCommand(command string) bool {
	parts := strings.Fields(command)
	if len(parts) == 0 {
		return false
	}

	cmd := strings.ToLower(parts[0])

	switch cmd {
	case "help", "h":
		app.showHelp()

	case "login", "l":
		app.handleLogin()

	case "logout":
		app.handleLogout()

	case "status", "s":
		app.showStatus()

	case "send":
		app.handleSend(parts[1:])

	case "friends", "f":
		app.showFriends()

	case "groups", "g":
		app.showGroups()

	case "autoreply":
		app.handleAutoReply(parts[1:])

	case "config":
		app.showConfig()

	case "quit", "exit", "q":
		fmt.Println("正在退出...")
		if app.bot.IsLoggedIn() {
			app.bot.Logout()
		}
		return true

	default:
		fmt.Printf("未知命令: %s\n", command)
		fmt.Println("输入 'help' 查看可用命令")
	}

	return false
}

// showHelp 显示帮助信息
func (app *ConsoleApp) showHelp() {
	fmt.Println("\n📖 可用命令:")
	fmt.Println("  login, l        - 登录微信")
	fmt.Println("  logout          - 退出登录")
	fmt.Println("  status, s       - 显示登录状态")
	fmt.Println("  send <目标> <消息> - 发送消息")
	fmt.Println("  friends, f      - 显示好友列表")
	fmt.Println("  groups, g       - 显示群组列表")
	fmt.Println("  autoreply on/off - 开启/关闭自动回复")
	fmt.Println("  config          - 显示配置信息")
	fmt.Println("  help, h         - 显示此帮助")
	fmt.Println("  quit, exit, q   - 退出程序")
	fmt.Println()
}

// handleLogin 处理登录命令
func (app *ConsoleApp) handleLogin() {
	if app.bot.IsLoggedIn() {
		fmt.Println("已经登录了！")
		return
	}

	fmt.Println("🔄 开始登录流程...")
	go func() {
		if err := app.bot.Login(); err != nil {
			fmt.Printf("登录失败: %v\n", err)
		}
	}()
}

// handleLogout 处理退出登录命令
func (app *ConsoleApp) handleLogout() {
	if !app.bot.IsLoggedIn() {
		fmt.Println("尚未登录！")
		return
	}

	app.bot.Logout()
}

// showStatus 显示状态
func (app *ConsoleApp) showStatus() {
	if app.bot.IsLoggedIn() {
		fmt.Println("✅ 状态: 已登录")
	} else {
		fmt.Println("❌ 状态: 未登录")
	}
}

// handleSend 处理发送消息命令
func (app *ConsoleApp) handleSend(args []string) {
	if !app.bot.IsLoggedIn() {
		fmt.Println("请先登录微信！")
		return
	}

	if len(args) < 2 {
		fmt.Println("用法: send <目标> <消息>")
		fmt.Println("示例: send 张三 你好")
		return
	}

	target := args[0]
	message := strings.Join(args[1:], " ")

	fmt.Printf("正在发送消息给 %s: %s\n", target, message)

	// 先尝试发送给好友
	err := app.bot.SendTextToFriend(target, message)
	if err != nil {
		// 如果发送给好友失败，尝试发送给群组
		err = app.bot.SendTextToGroup(target, message)
	}

	if err != nil {
		fmt.Printf("❌ 发送失败: %v\n", err)
	} else {
		fmt.Println("✅ 消息发送成功！")
	}
}

// showFriends 显示好友列表
func (app *ConsoleApp) showFriends() {
	if !app.bot.IsLoggedIn() {
		fmt.Println("请先登录微信！")
		return
	}

	friends, err := app.bot.GetFriends()
	if err != nil {
		fmt.Printf("获取好友列表失败: %v\n", err)
		return
	}

	fmt.Printf("\n👥 好友列表 (共 %d 人):\n", len(friends))
	for i, friend := range friends {
		name := friend.NickName
		if friend.RemarkName != "" {
			name = friend.RemarkName
		}
		fmt.Printf("  %d. %s\n", i+1, name)
	}
	fmt.Println()
}

// showGroups 显示群组列表
func (app *ConsoleApp) showGroups() {
	if !app.bot.IsLoggedIn() {
		fmt.Println("请先登录微信！")
		return
	}

	groups, err := app.bot.GetGroups()
	if err != nil {
		fmt.Printf("获取群组列表失败: %v\n", err)
		return
	}

	fmt.Printf("\n👥 群组列表 (共 %d 个):\n", len(groups))
	for i, group := range groups {
		fmt.Printf("  %d. %s\n", i+1, group.NickName)
	}
	fmt.Println()
}

// handleAutoReply 处理自动回复命令
func (app *ConsoleApp) handleAutoReply(args []string) {
	if len(args) == 0 {
		if app.config.AutoReply.Enabled {
			fmt.Println("🤖 自动回复: 已启用")
		} else {
			fmt.Println("🤖 自动回复: 已禁用")
		}
		return
	}

	switch strings.ToLower(args[0]) {
	case "on", "enable", "1":
		app.config.AutoReply.Enabled = true
		config.SaveConfig(app.config)
		fmt.Println("✅ 自动回复已启用")

	case "off", "disable", "0":
		app.config.AutoReply.Enabled = false
		config.SaveConfig(app.config)
		fmt.Println("❌ 自动回复已禁用")

	default:
		fmt.Println("用法: autoreply on/off")
	}
}

// showConfig 显示配置信息
func (app *ConsoleApp) showConfig() {
	fmt.Println("\n⚙️ 当前配置:")
	fmt.Printf("  自动回复: %v\n", app.config.AutoReply.Enabled)
	fmt.Printf("  关键词数量: %d\n", len(app.config.AutoReply.Keywords))
	fmt.Printf("  主题: %s\n", app.config.UI.Theme)
	fmt.Printf("  日志级别: %s\n", app.config.Log.Level)
	fmt.Println()
}

func main() {
	// 设置日志
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// 创建并运行控制台应用
	app := NewConsoleApp()
	app.Run()
}
