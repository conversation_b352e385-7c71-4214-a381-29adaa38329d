# 微信推送机器人GUI控制台

一个用户友好的微信机器人图形界面控制台，基于 openwechat 库开发，让小白用户也能轻松使用微信自动化功能。

## 功能特性

### 🎯 核心功能
- **图形界面操作** - 无需命令行，点击即可使用
- **微信登录管理** - 扫码登录，支持热登录
- **好友/群组管理** - 自动获取并显示联系人列表
- **消息发送** - 支持向好友和群组发送文本消息
- **自动回复** - 可配置关键词自动回复
- **实时日志** - 显示运行状态和消息记录

### 🛠️ 用户友好特性
- **傻瓜式操作** - 界面直观，操作简单
- **配置保存** - 自动保存用户设置
- **状态提示** - 实时显示登录和运行状态
- **错误处理** - 友好的错误提示和处理
- **二维码显示** - 自动弹出登录二维码

## 安装和使用

### 环境要求
- Go 1.20 或更高版本
- Windows/macOS/Linux 系统

### 快速开始

1. **克隆项目**
   ```bash
   git clone <项目地址>
   cd wechat-bot-gui
   ```

2. **安装依赖**
   ```bash
   go mod tidy
   ```

3. **运行程序**
   ```bash
   go run main.go
   ```

4. **使用步骤**
   - 启动程序后会打开图形界面
   - 点击"登录微信"按钮
   - 扫描弹出的二维码完成登录
   - 登录成功后可以看到好友和群组列表
   - 选择发送目标，输入消息，点击发送
   - 可以在设置中启用自动回复功能

### 编译可执行文件

```bash
# Windows
go build -o wechat-bot-gui.exe main.go

# macOS/Linux
go build -o wechat-bot-gui main.go
```

## 界面说明

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 状态: 已登录                    [登录微信] [退出登录]        │
├─────────────┬─────────────────┬─────────────────────────────┤
│   好友列表   │    消息发送      │        设置和日志            │
│             │                │                            │
│ • 张三      │ 发送目标: [选择] │ ☑ 启用自动回复              │
│ • 李四      │                │ [高级设置]                  │
│ • 王五      │ 消息内容:       │                            │
│             │ ┌─────────────┐ │ 运行日志:                   │
│   群组列表   │ │             │ │ ┌─────────────────────────┐ │
│             │ │             │ │ │ [时间] 登录成功          │ │
│ • 工作群    │ │             │ │ │ [时间] 收到消息...       │ │
│ • 朋友群    │ └─────────────┘ │ │ [时间] 发送消息...       │ │
│             │                │ │                         │ │
│             │   [发送消息]    │ └─────────────────────────┘ │
│             │                │ [清空日志]                  │
└─────────────┴─────────────────┴─────────────────────────────┘
```

### 功能说明

1. **状态栏** - 显示当前登录状态
2. **登录按钮** - 点击后弹出二维码进行登录
3. **好友列表** - 显示所有好友（显示备注名优先）
4. **群组列表** - 显示所有群组
5. **消息发送** - 选择目标并发送消息
6. **自动回复** - 开启后根据关键词自动回复
7. **运行日志** - 显示程序运行状态和消息记录

## 配置说明

程序会在用户目录下创建 `.wechat-bot-gui` 文件夹存储配置：

```
~/.wechat-bot-gui/
├── config.json      # 主配置文件
└── logs/            # 日志文件目录
```

### 配置文件示例 (config.json)

```json
{
  "auto_reply": {
    "enabled": false,
    "keywords": {
      "你好": "您好！我是自动回复机器人",
      "帮助": "请输入具体问题，我会尽快回复您",
      "ping": "pong"
    }
  },
  "push": {
    "enabled": false,
    "targets": [],
    "blacklist": []
  },
  "ui": {
    "theme": "light",
    "language": "zh",
    "auto_save": true
  },
  "log": {
    "level": "info",
    "save_file": true,
    "max_size": 10
  }
}
```

## 注意事项

1. **微信限制** - 请遵守微信使用规范，避免频繁操作
2. **网络环境** - 确保网络连接稳定
3. **防火墙** - 可能需要允许程序通过防火墙
4. **杀毒软件** - 部分杀毒软件可能误报，请添加信任

## 常见问题

### Q: 登录失败怎么办？
A: 检查网络连接，重新扫码登录，确保微信版本支持网页版登录。

### Q: 找不到好友或群组？
A: 登录成功后稍等片刻，程序会自动刷新联系人列表。

### Q: 自动回复不生效？
A: 确保已勾选"启用自动回复"，并检查关键词配置。

### Q: 程序崩溃怎么办？
A: 查看日志文件 `wechat-bot.log` 了解错误原因，重启程序。

## 技术架构

- **UI框架**: Fyne v2 - 跨平台GUI框架
- **微信SDK**: openwechat - Go语言微信SDK
- **配置管理**: JSON格式配置文件
- **日志系统**: Go标准库log包

## 开发计划

- [ ] 支持发送图片和文件
- [ ] 定时消息推送功能
- [ ] 消息模板管理
- [ ] 多账号支持
- [ ] 插件系统
- [ ] 数据统计和分析

## 许可证

本项目基于 Apache 2.0 许可证开源。

## 贡献

欢迎提交 Issue 和 Pull Request！
