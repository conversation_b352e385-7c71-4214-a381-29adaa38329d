# 微信推送机器人 - 快速开始指南

## 🚀 最简单的使用方法

### 方法一：Web界面版本（推荐小白用户）

1. **启动程序**
   ```bash
   # 在PowerShell或命令提示符中运行：
   go run web/server.go
   
   # 或者双击运行：
   run-web.bat
   ```

2. **打开浏览器**
   - 程序启动后会显示：`🌐 Web控制台已启动: http://localhost:8080`
   - 在浏览器中打开：`http://localhost:8080`

3. **使用步骤**
   - 点击"登录微信"按钮
   - 在日志区域查看二维码链接，点击链接打开
   - 使用微信扫描二维码登录
   - 登录成功后，输入好友昵称和消息内容
   - 点击"发送消息"

### 方法二：命令行版本（推荐技术用户）

1. **启动程序**
   ```bash
   # 在PowerShell或命令提示符中运行：
   go run cmd/console.go
   
   # 或者双击运行：
   run-console.bat
   ```

2. **使用命令**
   ```
   请输入命令 (输入 help 查看帮助): login
   请输入命令 (输入 help 查看帮助): send 张三 你好
   请输入命令 (输入 help 查看帮助): quit
   ```

## 📋 可用命令（命令行版本）

- `login` 或 `l` - 登录微信
- `logout` - 退出登录
- `status` 或 `s` - 显示登录状态
- `send <目标> <消息>` - 发送消息
- `friends` 或 `f` - 显示好友列表
- `groups` 或 `g` - 显示群组列表
- `autoreply on/off` - 开启/关闭自动回复
- `help` 或 `h` - 显示帮助
- `quit` 或 `exit` - 退出程序

## 🔧 环境要求

- Go 1.20 或更高版本
- 稳定的网络连接
- 支持网页版登录的微信账号

## ❓ 常见问题

### Q: 如何检查Go环境？
A: 在命令行中运行 `go version`，如果显示版本信息则表示已安装。

### Q: 启动脚本出现乱码怎么办？
A: 直接使用以下命令启动：
```bash
# Web版本
go run web/server.go

# 命令行版本
go run cmd/console.go
```

### Q: 登录失败怎么办？
A: 
1. 确保网络连接稳定
2. 检查微信是否支持网页版登录
3. 尝试重新扫码登录

### Q: 找不到好友或群组？
A: 
1. 确保已成功登录
2. 使用完整的好友昵称或备注名
3. 使用完整的群组名称

## 🎯 使用示例

### Web界面使用示例
1. 打开 `http://localhost:8080`
2. 点击"登录微信"
3. 扫描二维码登录
4. 在"发送目标"输入：`张三`
5. 在"消息内容"输入：`你好，这是测试消息`
6. 点击"发送消息"

### 命令行使用示例
```
请输入命令: login
[INFO] 开始登录流程...
[INFO] 请扫描二维码登录: https://login.weixin.qq.com/qrcode/xxx

请输入命令: send 张三 你好
正在发送消息给 张三: 你好
✅ 消息发送成功！

请输入命令: autoreply on
✅ 自动回复已启用

请输入命令: quit
正在退出...
```

## 📞 获取帮助

如果遇到问题：
1. 查看 `README.md` 获取详细文档
2. 查看 `使用说明.md` 获取完整指南
3. 检查程序运行日志了解错误信息

---

**提示**: 推荐小白用户使用Web界面版本，界面更友好，操作更简单！
