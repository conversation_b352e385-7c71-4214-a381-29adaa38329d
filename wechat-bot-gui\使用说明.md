# 微信推送机器人GUI控制台 - 使用说明

## 🎯 项目简介

这是一个用户友好的微信机器人控制台，提供了三种不同的使用方式，让小白用户也能轻松使用微信自动化功能。

## 📦 项目结构

```
wechat-bot-gui/
├── main.go              # 原始GUI版本（需要OpenGL支持）
├── cmd/console.go       # 命令行版本
├── web/server.go        # Web界面版本
├── bot/wechat_bot.go    # 微信机器人核心逻辑
├── config/config.go     # 配置管理
├── start.bat            # Windows启动脚本
├── start.sh             # Linux/macOS启动脚本
├── console.exe          # 命令行版本可执行文件
├── web-gui.exe          # Web版本可执行文件
└── README.md            # 详细文档
```

## 🚀 三种使用方式

### 1. 💻 命令行版本（推荐新手）

**特点：**
- 简单易用，无需额外依赖
- 交互式命令操作
- 适合服务器环境

**启动方式：**
```bash
# 直接运行
./console.exe

# 或者从源码运行
go run cmd/console.go
```

**可用命令：**
- `login` 或 `l` - 登录微信
- `logout` - 退出登录
- `status` 或 `s` - 显示登录状态
- `send <目标> <消息>` - 发送消息
- `friends` 或 `f` - 显示好友列表
- `groups` 或 `g` - 显示群组列表
- `autoreply on/off` - 开启/关闭自动回复
- `help` 或 `h` - 显示帮助
- `quit` 或 `exit` - 退出程序

**使用示例：**
```
请输入命令 (输入 help 查看帮助): login
🔄 开始登录流程...
📱 请扫描二维码登录: https://login.weixin.qq.com/qrcode/xxx

请输入命令 (输入 help 查看帮助): send 张三 你好
正在发送消息给 张三: 你好
✅ 消息发送成功！
```

### 2. 🌐 Web界面版本（推荐小白）

**特点：**
- 美观的Web界面
- 实时状态更新
- 支持所有现代浏览器
- 最用户友好

**启动方式：**
```bash
# 直接运行
./web-gui.exe

# 或者从源码运行
go run web/server.go
```

**使用步骤：**
1. 运行程序后会显示：`🌐 Web控制台已启动: http://localhost:8080`
2. 在浏览器中打开 `http://localhost:8080`
3. 点击"登录微信"按钮
4. 在日志区域查看二维码链接，点击链接在新窗口打开
5. 使用微信扫描二维码登录
6. 登录成功后即可发送消息

**界面功能：**
- 🔑 登录控制：一键登录/退出
- 📤 消息发送：输入目标和内容即可发送
- ⚙️ 设置：开启/关闭自动回复
- 📋 运行日志：实时显示程序运行状态

### 3. 🖥️ 桌面GUI版本（高级用户）

**特点：**
- 原生桌面应用
- 功能最完整
- 需要OpenGL支持

**启动方式：**
```bash
go run main.go
```

**注意：** 此版本在某些Windows环境下可能遇到OpenGL依赖问题，建议使用Web版本。

## 🔧 安装和配置

### 环境要求
- Go 1.20 或更高版本
- Windows/macOS/Linux 系统
- 稳定的网络连接

### 快速安装
1. 下载或克隆项目到本地
2. 进入项目目录
3. 运行 `go mod tidy` 安装依赖
4. 选择一种方式启动程序

### 一键启动脚本

**Windows用户：**
双击 `start.bat` 文件即可自动检查环境并启动程序

**Linux/macOS用户：**
```bash
chmod +x start.sh
./start.sh
```

## ⚙️ 配置说明

程序会自动在用户目录下创建配置文件：
- Windows: `C:\Users\<USER>\.wechat-bot-gui\config.json`
- Linux/macOS: `~/.wechat-bot-gui/config.json`

### 配置文件示例
```json
{
  "auto_reply": {
    "enabled": false,
    "keywords": {
      "你好": "您好！我是自动回复机器人",
      "帮助": "请输入具体问题，我会尽快回复您",
      "ping": "pong"
    }
  },
  "ui": {
    "theme": "light",
    "language": "zh",
    "auto_save": true
  },
  "log": {
    "level": "info",
    "save_file": true,
    "max_size": 10
  }
}
```

## 📝 使用技巧

### 发送消息技巧
1. **目标名称要准确**：必须是好友的昵称或备注名，群组的完整名称
2. **支持模糊匹配**：程序会先尝试发送给好友，失败后尝试发送给群组
3. **批量发送**：可以通过脚本调用API实现批量发送

### 自动回复设置
1. 在配置文件中添加关键词和回复内容
2. 支持部分匹配，包含关键词即可触发
3. 可以设置多个关键词对应同一个回复

### 安全建议
1. 不要频繁发送消息，避免被微信限制
2. 遵守微信使用规范
3. 定期备份重要配置
4. 不要在公共网络环境下使用

## 🐛 常见问题

### Q: 登录失败怎么办？
A: 
1. 检查网络连接是否稳定
2. 确保微信版本支持网页版登录
3. 尝试重新扫码登录
4. 检查防火墙设置

### Q: 找不到好友或群组？
A: 
1. 确保已经成功登录
2. 检查目标名称是否完全正确
3. 对于好友，尝试使用备注名
4. 对于群组，使用完整的群名称

### Q: 自动回复不生效？
A: 
1. 确保已启用自动回复功能
2. 检查关键词配置是否正确
3. 查看日志确认是否收到消息
4. 重启程序重新加载配置

### Q: Web界面无法访问？
A: 
1. 确认程序已正常启动
2. 检查端口8080是否被占用
3. 尝试使用 `http://127.0.0.1:8080`
4. 检查防火墙是否阻止了连接

## 🔄 更新日志

### v1.0.0
- ✅ 实现基础微信登录功能
- ✅ 支持发送文本消息
- ✅ 添加自动回复功能
- ✅ 提供三种使用方式
- ✅ 完善配置管理
- ✅ 添加详细日志记录

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件了解详细错误信息
2. 检查配置文件是否正确
3. 参考常见问题解答
4. 提交Issue描述问题

## 📄 许可证

本项目基于 Apache 2.0 许可证开源。
