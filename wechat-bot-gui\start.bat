@echo off
chcp 65001 >nul
title 微信推送机器人GUI控制台

echo ========================================
echo    微信推送机器人GUI控制台
echo ========================================
echo.

:: 检查Go环境
go version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Go环境，请先安装Go语言环境
    echo 下载地址: https://golang.org/dl/
    echo.
    pause
    exit /b 1
)

echo [信息] Go环境检测正常
echo.

:: 检查依赖
echo [信息] 正在检查和安装依赖...
go mod tidy
if errorlevel 1 (
    echo [错误] 依赖安装失败
    pause
    exit /b 1
)

echo [信息] 依赖安装完成
echo.

:: 运行程序
echo [信息] 正在启动微信推送机器人...
echo [提示] 程序启动后会打开图形界面窗口
echo [提示] 如需退出，请关闭图形界面窗口或按Ctrl+C
echo.

go run main.go

echo.
echo [信息] 程序已退出
pause
