package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 应用配置结构
type Config struct {
	// 自动回复设置
	AutoReply struct {
		Enabled  bool              `json:"enabled"`
		Keywords map[string]string `json:"keywords"` // 关键词 -> 回复内容
	} `json:"auto_reply"`

	// 推送设置
	Push struct {
		Enabled   bool     `json:"enabled"`
		Targets   []string `json:"targets"`   // 推送目标（好友昵称或群名）
		Blacklist []string `json:"blacklist"` // 黑名单
	} `json:"push"`

	// 界面设置
	UI struct {
		Theme    string `json:"theme"`     // light/dark
		Language string `json:"language"`  // zh/en
		AutoSave bool   `json:"auto_save"` // 自动保存配置
	} `json:"ui"`

	// 日志设置
	Log struct {
		Level    string `json:"level"`     // debug/info/warn/error
		SaveFile bool   `json:"save_file"` // 是否保存到文件
		MaxSize  int    `json:"max_size"`  // 日志文件最大大小(MB)
	} `json:"log"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	config := &Config{}
	
	// 自动回复默认设置
	config.AutoReply.Enabled = false
	config.AutoReply.Keywords = map[string]string{
		"你好":   "您好！我是自动回复机器人",
		"帮助":   "请输入具体问题，我会尽快回复您",
		"ping": "pong",
	}

	// 推送默认设置
	config.Push.Enabled = false
	config.Push.Targets = []string{}
	config.Push.Blacklist = []string{}

	// 界面默认设置
	config.UI.Theme = "light"
	config.UI.Language = "zh"
	config.UI.AutoSave = true

	// 日志默认设置
	config.Log.Level = "info"
	config.Log.SaveFile = true
	config.Log.MaxSize = 10

	return config
}

// GetConfigPath 获取配置文件路径
func GetConfigPath() string {
	homeDir, _ := os.UserHomeDir()
	configDir := filepath.Join(homeDir, ".wechat-bot-gui")
	os.MkdirAll(configDir, 0755)
	return filepath.Join(configDir, "config.json")
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	configPath := GetConfigPath()
	
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		config := DefaultConfig()
		SaveConfig(config)
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return DefaultConfig(), err
	}

	config := &Config{}
	if err := json.Unmarshal(data, config); err != nil {
		return DefaultConfig(), err
	}

	return config, nil
}

// SaveConfig 保存配置
func SaveConfig(config *Config) error {
	configPath := GetConfigPath()
	
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}
